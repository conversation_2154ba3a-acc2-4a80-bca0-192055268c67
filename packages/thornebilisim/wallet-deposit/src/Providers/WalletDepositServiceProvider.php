<?php

namespace Thorne\WalletDeposit\Providers;

use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;
use Thorne\QontoDeposit\Observers\CustomerObserver;
use Thorne\WalletDeposit\Http\Middleware\RequireKycVerification;
use Thorne\WalletDeposit\Models\WalletTransaction;
use Webkul\Customer\Models\Customer;

class WalletDepositServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $packageRoot = dirname(__DIR__, 2);

        foreach ([
            'core'           => 'system.php',
            'wallet-deposit' => 'wallet-deposit.php',
        ] as $namespace => $file) {
            $path = "{$packageRoot}/config/{$file}";
            if (file_exists($path)) {
                $this->mergeConfigFrom($path, $namespace);
            }
        }
    }

    public function boot(Router $router): void
    {
        $router->aliasMiddleware('require.kyc', RequireKycVerification::class);

        $packageRoot = dirname(__DIR__, 2);

        $this->loadTranslationsFrom($packageRoot.'/resources/lang', 'wallet-deposit');
        $this->loadViewsFrom($packageRoot.'/resources/views', 'wallet-deposit');
        $this->loadRoutesFrom($packageRoot.'/routes/routes.php');
        $this->loadMigrationsFrom($packageRoot.'/database/migrations');

        $this->publishes([
            $packageRoot.'/config/wallet-deposit.php' => config_path('wallet-deposit.php'),
            $packageRoot.'/resources/lang'            => resource_path('lang/vendor/wallet-deposit'),
        ], 'wallet-deposit');

        $this->registerCustomerRelations();
    }

    private function registerCustomerRelations(): void
    {
        Customer::resolveRelationUsing('walletTransactions', function (Customer $customer) {
            return $customer->hasMany(WalletTransaction::class, 'customer_id');
        });
        Customer::observe(CustomerObserver::class);
    }
}
