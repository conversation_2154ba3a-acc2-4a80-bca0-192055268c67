<?php

namespace Thorne\WalletDeposit\Http\Controllers;

use App\Services\CurrencyConverter;
use App\Services\OAuth\ApiClientAuth10Service;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Thorne\WalletDeposit\Enums\TransactionDirection;
use Thorne\WalletDeposit\Enums\TransactionStatus;
use Thorne\WalletDeposit\Enums\TransactionType;
use Thorne\WalletDeposit\Services\WalletDepositService;
use Webkul\Customer\Models\Customer;

class WalletController extends Controller
{
    public array|Collection $sources = [];

    public ?Customer $customer = null;

    private ApiClientAuth10Service $apiService;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check()) {
                return redirect()->route('customer.session.index');
            }

            $this->customer   = auth()->guard('customer')->user();
            $this->sources    = $this->getSources();
            $this->apiService = app(ApiClientAuth10Service::class);

            return $next($request);
        });
    }

    public function getSources()
    {
        $walletSources = app(WalletDepositService::class);

        return $walletSources->getWalletSources();
    }

    public function sources(): JsonResponse
    {
        $this->sources = $this->getSources();

        return response()->json([
            'success' => true,
            'data'    => $this->sources,
        ]);
    }

    public function withdraw(): View|RedirectResponse
    {
        if (request()->isMethod('post')) {
            $walletTransaction = $this->createWithdraw();
            if ($walletTransaction) {
                return redirect()->route('customer.account.wallet.transaction', $walletTransaction->id);
            } else {
                return redirect()->back()->with('error', __('wallet-deposit::app.ui.pages.withdraw.errors.withdraw-failed'));
            }
        }

        $this->sources = $this->getSources();
        $this->sources = collect($this->sources)->filter(function ($source) {
            return $source['can_withdraw'];
        })->values()->all();

        $currency           = core()->getBaseCurrencyCode();
        $maxAmount          = collect($this->balance())->get($currency);
        $hasPendingWithdraw = $this->customer->walletTransactions()
            ->where('type', TransactionType::WITHDRAWAL)
            ->where('status', '!=', TransactionStatus::COMPLETED)
            ->exists();

        return view('wallet-deposit::withdraw', [
            'sources'            => $this->sources,
            'customer'           => $this->customer,
            'currency'           => $currency,
            'maxAmount'          => $maxAmount,
            'hasPendingWithdraw' => $hasPendingWithdraw,
        ]);
    }

    public function createWithdraw()
    {
        $methods = collect($this->sources)->pluck('source_key')->all();

        $currency  = core()->getBaseCurrencyCode();
        $maxAmount = collect($this->balance())->get($currency);

        $data = request()->validate([
            'withdraw-method' => ['required', Rule::in($methods)],
            'account-holder'  => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (trim($value) !== trim($this->customer->name)) {
                        $fail(__('wallet-deposit::app.ui.pages.withdraw.errors.invalid-account-holder'));
                    }
                },
            ],
            'account-number'  => ['required', 'string'],
            'amount'          => ['required', 'numeric', 'min:1', 'max:'.$maxAmount],
            'currency'        => ['required', 'string', 'in:'.$currency],
        ]);

        DB::beginTransaction();
        $hasPendingWithdraw = $this->customer->walletTransactions()
            ->where('type', TransactionType::WITHDRAWAL)
            ->where('status', '!=', TransactionStatus::COMPLETED)
            ->exists();
        if ($hasPendingWithdraw) {
            DB::rollBack();

            return false;
        }

        $walletTransaction = $this->customer->walletTransactions()->create([
            'source'    => $data['withdraw-method'],
            'source_id' => $data['account-number'],
            'type'      => TransactionType::WITHDRAWAL,
            'direction' => TransactionDirection::OUTBOUND,
            'status'    => TransactionStatus::PENDING,
            'currency'  => $data['currency'],
            'amount'    => $data['amount'],
            'raw_data'  => $data,
        ]);

        DB::commit();

        return $walletTransaction;
    }

    public function balance()
    {
        $payload = [
            'userId'   => $this->customer->id,
            'tenantId' => 50,
            'currency' => '',
            'chainId'  => 'mirum-testnet',
        ];

        $cacheKey = 'api_wallet_balance_'.$this->customer->id;

        if (request()->expectsJson()) {
            cache()->forget($cacheKey);
        }

        $response = cache()->remember($cacheKey, now()->addMinutes(30), function () use ($payload) {
            return $this->apiService->baseUrl('/wallet/balance')
                ->baseMethod('post')
                ->baseClient($payload);
        });

        $converter = app(CurrencyConverter::class);

        $balances = collect($response->json('balances'))
            ->where('isStableCoin', true)
            ->mapWithKeys(fn ($item) => [
                $item['isoCurrency'] => (float) $converter->convertToMajorUnits((string) $item['balance'], $item['exponent']),
            ])
            ->all();

        if (request()->expectsJson()) {
            $currency = core()->getBaseCurrencyCode();

            return response()->json([
                'success'  => true,
                'amount'   => $balances[$currency] ?? 0,
                'currency' => $currency,
            ]);
        }

        return $balances;
    }

    public function transaction($transactionId)
    {
        $walletTransaction = $this->customer->walletTransactions()->findOrFail($transactionId);

        $sourceDepositUrl = collect($this->sources)->where('source_key', $walletTransaction->source)->pluck('route')->first();
        $historyRoute     = str_replace('deposit', 'history', $sourceDepositUrl);

        return redirect($historyRoute);
    }
}
