<?php

namespace Thorne\WalletDeposit\Services;

use Thorne\WalletDeposit\Http\Controllers\MoneyTransferController;

class IntegrationRegistryService
{
    protected static array $keys = [
        'CASH',
        MoneyTransferController::SOURCE,
    ];

    public static function register(string $key): void
    {
        if (! in_array($key, static::$keys, true)) {
            static::$keys[] = $key;
        }
    }

    public static function all(): array
    {
        return static::$keys;
    }
}
