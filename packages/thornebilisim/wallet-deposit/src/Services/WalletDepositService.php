<?php

namespace Thorne\WalletDeposit\Services;

use App\Models\MetaData;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class WalletDepositService
{
    protected string $corePath;

    protected string $configPath;

    public function __construct()
    {
        $this->corePath   = 'sales.wallet-deposit';
        $this->configPath = 'wallet-deposit.sources';
    }

    public function getCoreConfig(string $key, $default = null, bool $useConfig = false)
    {
        $path  = "{$this->corePath}.{$key}";
        $value = core()->getConfigData($path);

        if ($value !== null && $value !== '') {
            return $value;
        }

        if ($useConfig) {
            $configValue = config("{$this->configPath}.{$key}");
            if ($configValue !== null && $configValue !== '') {
                return $configValue;
            }
        }

        return $default;
    }

    public function isExternalSource(string $key): bool
    {
        $defaultConfig = 'wallet-deposit';
        $configValue   = config("{$this->configPath}.{$key}.config");

        if ($configValue === $defaultConfig) {
            return false;
        }

        $externalKey = "{$key}-deposit.sources.{$key}.config";

        return config($externalKey, null) ?? false;
    }

    public function isAvailable(string $key): bool
    {
        $status       = (bool) $this->getCoreConfig('settings.status', default: false, useConfig: true);
        $sourceStatus = (bool) $this->getCoreConfig("{$key}.status", default: false, useConfig: true);

        return $status && $sourceStatus;
    }

    public function generateUniqueReferenceCode(): string
    {
        $prefix = $this->getCoreConfig('settings.reference_code_prefix');

        $code = $prefix.strtoupper(Str::random(6));

        if ($this->referenceCodeExists($code)) {
            return $this->generateUniqueReferenceCode();
        }

        return $code;
    }

    protected function referenceCodeExists(string $code): bool
    {
        return MetaData::where('key', 'reference_code')
            ->where('text_value', $code)
            ->exists();
    }

    public function getWalletSources()
    {
        return collect(IntegrationRegistryService::all())
            ->mapWithKeys(function ($key) {
                $configKey = strtolower($key);

                return [
                    $configKey => config("wallet-deposit.sources.{$configKey}", config("{$configKey}-deposit.sources.{$configKey}", [])),
                ];
            })
            ->map(function ($method, $key) {
                $code         = $method['code'] ?? '';

                $status = $this->isAvailable($key);
                $route  = $this->isExternalSource($key) ? config("{$key}-deposit.sources.{$code}.route") : $this->getCoreConfig("{$code}.route", default: '', useConfig: true);

                $isExternal  = $this->isExternalSource($key);
                $label       = $isExternal ? __("{$code}-deposit::app.ui.pages.withdraw.method-title.{$code}") : __("wallet-deposit::app.ui.pages.withdraw.method-title.{$code}");
                $placeholder = $isExternal ? __("{$code}-deposit::app.ui.pages.withdraw.method-placeholder.{$code}") : __("wallet-deposit::app.ui.pages.withdraw.method-placeholder.{$code}");

                return [
                    ...$method,
                    'code'          => $key,
                    'source_key'    => collect(IntegrationRegistryService::all())->first(fn ($value) => $value === strtoupper($key)),
                    'title'         => $this->getCoreConfig("{$code}.title", __("{$code}-deposit::app.ui.pages.withdraw.method-title.{$code}")),
                    'label'         => $label,
                    'placeholder'   => $placeholder,
                    'status'        => $status,
                    'route'         => $status && $route ? route($route) : '#',
                    'can_deposit'   => $this->canDeposit($code),
                    'can_withdraw'  => $this->canWithdraw($code),
                    'base_currency' => core()->getBaseCurrencyCode(),
                    'address'       => $this->getAddress($code),
                ];
            })
            ->sortBy('sort')
            ->values()
            ->all();
    }

    protected function canDeposit(string $sourceCode): bool
    {
        $status       = $this->isAvailable($sourceCode);
        $canDeposit   = (bool) $this->getCoreConfig("{$sourceCode}.can_deposit", default: false, useConfig: true);

        if (auth()->guard('customer')->check()) {
            return $status && $canDeposit;
        }

        return false;
    }

    protected function canWithdraw(string $sourceCode): bool
    {
        $status       = $this->isAvailable($sourceCode);
        $canWithdraw  = (bool) $this->getCoreConfig("{$sourceCode}.can_withdraw", default: false, useConfig: true);

        if (auth()->guard('customer')->check()) {
            // if ($customer = auth()->guard('customer')->user()) {
            //     return $status && $canWithdraw && $customer->hasMeta("{$sourceCode}-address");
            // }
            return $status && $canWithdraw;
        }

        return false;
    }

    protected function getAddress(string $sourceCode): array|Collection
    {
        $address = [];

        if ($customer = auth()->guard('customer')->user()) {
            if ($customer->hasMeta("{$sourceCode}-address")) {
                $address = $customer->getMetas("{$sourceCode}-address")->pluck('value');
            }
        }

        return $address;
    }
}
