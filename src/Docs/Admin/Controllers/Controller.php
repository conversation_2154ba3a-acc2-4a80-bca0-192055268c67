<?php

namespace Webkul\RestApi\Docs\Admin\Controllers;

/**
 * @OA\Info(
 *      version="2.2.0",
 *      title="Bagisto Admin Rest API Documentation",
 *      description="Bagisto Admin Rest API Documentation",
 *
 *      @OA\Contact(
 *          email="<EMAIL>"
 *      )
 * )
 *
 * @OA\Server(
 *      url=APP_URL,
 *      description="Bagisto Admin End Rest API"
 * )
 *
 * @OA\Tag(
 *     name="Authentication",
 *     description="API Endpoints of Admin user's authentication"
 * )
 * @OA\Tag(
 *     name="Users",
 *     description="API Endpoints of Admin User"
 * )
 * @OA\Tag(
 *     name="Categories",
 *     description="API Endpoints of Category"
 * )
 * @OA\Tag(
 *     name="Attributes",
 *     description="API Endpoints of Attribute"
 * )
 * @OA\Tag(
 *     name="Attribute-Families",
 *     description="API Endpoints of Attribute-Family"
 * )
 * @OA\Tag(
 *     name="Products",
 *     description="API Endpoints of Product"
 * )
 * @OA\Tag(
 *     name="Orders",
 *     description="API Endpoints of Admin Order"
 * )
 * @OA\Tag(
 *     name="ReOrders",
 *     description="API Endpoints of Admin Re Order"
 * )
 * @OA\Tag(
 *     name="Invoices",
 *     description="API Endpoints of Admin Invoice"
 * )
 * @OA\Tag(
 *     name="Shipments",
 *     description="API Endpoints of Admin Shipment"
 * )
 * @OA\Tag(
 *     name="Refunds",
 *     description="API Endpoints of Admin Refund"
 * )
 * @OA\Tag(
 *     name="Transactions",
 *     description="API Endpoints of Admin Transaction"
 * )
 * @OA\Tag(
 *     name="Locales",
 *     description="API Endpoints of Admin Locale"
 * )
 * @OA\Tag(
 *     name="Currencies",
 *     description="API Endpoints of Admin Currency"
 * )
 * @OA\Tag(
 *     name="Exchange-Rates",
 *     description="API Endpoints of Admin Exchange-Rate"
 * )
 * @OA\Tag(
 *     name="Inventory-Sources",
 *     description="API Endpoints of Admin Inventory-Source"
 * )
 * @OA\Tag(
 *     name="Channels",
 *     description="API Endpoints of Admin Channel"
 * )
 * @OA\Tag(
 *     name="Roles",
 *     description="API Endpoints of Admin Role"
 * )
 * @OA\Tag(
 *     name="Tax-Categories",
 *     description="API Endpoints of Admin Tax-Category"
 * )
 * @OA\Tag(
 *     name="Tax-Rates",
 *     description="API Endpoints of Admin Tax-Rate"
 * )
 * @OA\Tag(
 *     name="Configurations",
 *     description="API Endpoints of Admin Configuration"
 * )
 * @OA\Tag(
 *     name="Cms-Pages",
 *     description="API Endpoints of Admin Cms-Page"
 * )
 * @OA\Tag(
 *     name="Customers",
 *     description="API Endpoints of Admin Customer"
 * )
 * @OA\Tag(
 *     name="CustomerGroups",
 *     description="API Endpoints of Admin CustomerGroup"
 * )
 * @OA\Tag(
 *     name="CustomerReviews",
 *     description="API Endpoints of Admin CustomerReview"
 * )
 * @OA\Tag(
 *     name="CustomerAddresses",
 *     description="API Endpoints of Admin CustomerAddress"
 * )
 *  @OA\Tag(
 *     name="CustomerGDPR",
 *     description="API Endpoints of Admin CustomerGDPR"
 * )
 * @OA\Tag(
 *     name="CatalogRules",
 *     description="API Endpoints of Admin CatalogRule"
 * )
 * @OA\Tag(
 *     name="CartRules",
 *     description="API Endpoints of Admin CartRule"
 * )
 * @OA\Tag(
 *     name="CartRuleCoupons",
 *     description="API Endpoints of Admin CartRuleCoupon"
 * )
 * @OA\Tag(
 *     name="EmailTemplates",
 *     description="API Endpoints of Admin EmailTemplate"
 * )
 * @OA\Tag(
 *     name="Events",
 *     description="API Endpoints of Admin Event"
 * )
 * @OA\Tag(
 *     name="Campaigns",
 *     description="API Endpoints of Admin Campaign"
 * )
 * @OA\Tag(
 *     name="Newsletter Subscriptions",
 *     description="API Endpoints of Admin Newsletter Subscriptions"
 * )
 * @OA\Tag(
 *     name="SearchSynonym",
 *     description="API Endpoints of Admin SearchSynonym"
 * )
 * @OA\Tag(
 *     name="Sitemaps",
 *     description="API Endpoints of Admin Sitemaps"
 * )
 * @OA\Tag(
 *     name="URLRewrite",
 *     description="API Endpoints of Admin URLRewrite"
 * )
 * @OA\Tag(
 *     name="SearchTerms",
 *     description="API Endpoints of Admin SearchTerms"
 * )
 * @OA\Tag(
 *     name="Themes",
 *     description="API Endpoints of Themes customization"
 * )
 * @OA\Tag(
 *     name="CustomersReporting",
 *     description="API Endpoints of Customers Reporting"
 * )
 * @OA\Tag(
 *     name="SalesReporting",
 *     description="API Endpoints of Sales Reporting"
 * )
 * @OA\Tag(
 *     name="ProductsReporting",
 *     description="API Endpoints of Product Reporting"
 * )
 */
class Controller
{
}
